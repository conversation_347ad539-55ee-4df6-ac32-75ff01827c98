{"speed": {"fastllama": {"seq512_batch1": {"avg_time": 1.2715802192687988, "std_time": 0.0, "times": [1.2715802192687988]}}, "baseline": {"seq512_batch1": {"avg_time": 1.0546610355377197, "std_time": 0.0, "times": [1.0546610355377197]}}, "speedup": {"seq512_batch1": 0.82940975296406}}, "memory": {"fastllama": {"seq512_batch1": {"gpu_allocated_gb": 6.007482528686523, "gpu_reserved_gb": 6.513671875, "gpu_max_allocated_gb": 6.495747089385986, "gpu_max_reserved_gb": 6.513671875, "cpu_rss_gb": 1.6323623657226562, "cpu_vms_gb": 9.914226531982422, "cpu_percent": 5.130082724822806, "system_total_gb": 31.819416046142578, "system_available_gb": 16.644390106201172, "system_used_percent": 47.7}}, "baseline": {"seq512_batch1": {"gpu_allocated_gb": 5.15371036529541, "gpu_reserved_gb": 5.349609375, "gpu_max_allocated_gb": 5.242588996887207, "gpu_max_reserved_gb": 5.349609375, "cpu_rss_gb": 1.4923591613769531, "cpu_vms_gb": 8.571578979492188, "cpu_percent": 4.690089721360144, "system_total_gb": 31.819416046142578, "system_available_gb": 16.555335998535156, "system_used_percent": 48.0}}, "memory_savings": {"seq512_batch1": -0.23903420490197558}}, "early_exit": {"exit_rates": {"seq512_thresh0.7": 0.0, "seq512_thresh0.8": 0.0, "seq512_thresh0.9": 0.0}, "speedups": {"seq512_thresh0.7": 0.0, "seq512_thresh0.8": 0.0, "seq512_thresh0.9": 0.0}, "confidence_scores": {"seq512_thresh0.7": 0, "seq512_thresh0.8": 0, "seq512_thresh0.9": 0}}, "config": {"device": "cuda", "hidden_size": 2048, "intermediate_size": 11008, "num_attention_heads": 16, "num_key_value_heads": 4, "num_hidden_layers": 16, "vocab_size": 32000, "max_position_embeddings": 1048576, "rope_theta": 10000.0, "rope_scaling": {"type": "linear", "factor": 256.0}, "hidden_act": "silu", "rms_norm_eps": 1e-06, "local_attention_window": 512, "sparse_attention_stride": 8, "compression_ratio": 20, "local_layers": [1, 2, 3, 4, 5, 6, 7, 8], "sparse_layers": [9, 10, 11, 12, 13, 14, 15, 16], "hierarchical_layers": [17, 18, 19, 20, 21, 22, 23, 24], "full_attention_layers": [25, 26, 27, 28, 29, 30, 31, 32], "early_exit_layers": [2, 4, 8], "confidence_threshold": 0.8, "enable_early_exit": true, "compression_encoder_layers": 4, "enable_context_compression": true, "progressive_compression": true, "use_gradient_checkpointing": true, "gradient_checkpointing_ratio": 0.5, "use_mixed_precision": true, "kv_cache_quantization": true, "parameter_sharing": true, "initializer_range": 0.02, "use_cache": true, "pad_token_id": null, "bos_token_id": 1, "eos_token_id": 2, "use_flash_attention": true, "use_kernel_fusion": true, "enable_speculative_decoding": true, "max_batch_size": 32, "dynamic_batching": true, "memory_aware_batching": true, "output_attentions": false, "output_hidden_states": false, "use_return_dict": true}, "test_config": {"sequence_lengths": [512], "batch_sizes": [1], "confidence_thresholds": [0.7, 0.8, 0.9], "num_runs": 1, "device": "cuda"}}