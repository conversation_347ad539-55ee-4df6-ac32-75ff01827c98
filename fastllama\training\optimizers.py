"""
Optimized optimizers for FastLLaMA training.

Implements memory-efficient optimizers with dynamic loss scaling and
adaptive learning rate scheduling.
"""

import torch
import torch.nn as nn
from torch.optim import Adam<PERSON>
from torch.optim.lr_scheduler import LambdaLR, CosineAnnealingLR
import math
from typing import Dict, List, Optional, Union


class FastLLaMAOptimizer:
    """
    Optimized optimizer wrapper for FastLLaMA with:
    - Memory-efficient parameter grouping
    - Dynamic loss scaling
    - Adaptive learning rate scheduling
    - Parameter-specific optimization
    """
    
    def __init__(
        self,
        model: nn.Module,
        learning_rate: float = 5e-5,
        weight_decay: float = 0.01,
        beta1: float = 0.9,
        beta2: float = 0.999,
        epsilon: float = 1e-8,
        warmup_steps: int = 1000,
        total_steps: Optional[int] = None,
        scheduler_type: str = "cosine_with_warmup",
    ):
        self.model = model
        self.learning_rate = learning_rate
        self.warmup_steps = warmup_steps
        self.total_steps = total_steps
        self.scheduler_type = scheduler_type
        
        # Create parameter groups with different optimization strategies
        self.param_groups = self._create_parameter_groups(weight_decay)
        
        # Initialize optimizer
        self.optimizer = AdamW(
            self.param_groups,
            lr=learning_rate,
            betas=(beta1, beta2),
            eps=epsilon,
        )
        
        # Initialize scheduler
        self.scheduler = self._create_scheduler()
        
        # Dynamic loss scaling
        self.loss_scale = 1.0
        self.loss_scale_window = 1000
        self.loss_scale_factor = 2.0
        self.min_loss_scale = 1e-4
        self.scale_up_counter = 0
        self.scale_down_counter = 0
    
    def _create_parameter_groups(self, weight_decay: float) -> List[Dict]:
        """Create parameter groups with different optimization strategies."""
        # Separate parameters by type for different optimization
        embedding_params = []
        attention_params = []
        mlp_params = []
        norm_params = []
        compression_params = []
        other_params = []
        
        for name, param in self.model.named_parameters():
            if not param.requires_grad:
                continue
                
            if "embed" in name:
                embedding_params.append(param)
            elif "attention" in name or "attn" in name:
                attention_params.append(param)
            elif "mlp" in name or "feed_forward" in name:
                mlp_params.append(param)
            elif "norm" in name or "layernorm" in name:
                norm_params.append(param)
            elif "compress" in name:
                compression_params.append(param)
            else:
                other_params.append(param)
        
        param_groups = []
        
        # Embedding parameters - lower learning rate
        if embedding_params:
            param_groups.append({
                "params": embedding_params,
                "lr": self.learning_rate * 0.5,
                "weight_decay": weight_decay * 0.5,
                "name": "embeddings"
            })
        
        # Attention parameters - standard learning rate
        if attention_params:
            param_groups.append({
                "params": attention_params,
                "lr": self.learning_rate,
                "weight_decay": weight_decay,
                "name": "attention"
            })
        
        # MLP parameters - standard learning rate
        if mlp_params:
            param_groups.append({
                "params": mlp_params,
                "lr": self.learning_rate,
                "weight_decay": weight_decay,
                "name": "mlp"
            })
        
        # Normalization parameters - no weight decay
        if norm_params:
            param_groups.append({
                "params": norm_params,
                "lr": self.learning_rate,
                "weight_decay": 0.0,
                "name": "normalization"
            })
        
        # Compression parameters - higher learning rate for faster adaptation
        if compression_params:
            param_groups.append({
                "params": compression_params,
                "lr": self.learning_rate * 2.0,
                "weight_decay": weight_decay * 0.5,
                "name": "compression"
            })
        
        # Other parameters
        if other_params:
            param_groups.append({
                "params": other_params,
                "lr": self.learning_rate,
                "weight_decay": weight_decay,
                "name": "other"
            })
        
        return param_groups
    
    def _create_scheduler(self):
        """Create learning rate scheduler."""
        if self.scheduler_type == "cosine_with_warmup":
            return self._get_cosine_schedule_with_warmup()
        elif self.scheduler_type == "linear_with_warmup":
            return self._get_linear_schedule_with_warmup()
        elif self.scheduler_type == "polynomial":
            return self._get_polynomial_decay_schedule_with_warmup()
        else:
            return None
    
    def _get_cosine_schedule_with_warmup(self):
        """Cosine learning rate schedule with warmup."""
        def lr_lambda(current_step):
            if current_step < self.warmup_steps:
                return float(current_step) / float(max(1, self.warmup_steps))
            
            if self.total_steps is None:
                return 0.5 * (1.0 + math.cos(math.pi * current_step / 10000))
            
            progress = float(current_step - self.warmup_steps) / float(max(1, self.total_steps - self.warmup_steps))
            return max(0.0, 0.5 * (1.0 + math.cos(math.pi * progress)))
        
        return LambdaLR(self.optimizer, lr_lambda)
    
    def _get_linear_schedule_with_warmup(self):
        """Linear learning rate schedule with warmup."""
        def lr_lambda(current_step):
            if current_step < self.warmup_steps:
                return float(current_step) / float(max(1, self.warmup_steps))
            
            if self.total_steps is None:
                return 1.0
            
            return max(0.0, float(self.total_steps - current_step) / float(max(1, self.total_steps - self.warmup_steps)))
        
        return LambdaLR(self.optimizer, lr_lambda)
    
    def _get_polynomial_decay_schedule_with_warmup(self, power: float = 1.0):
        """Polynomial decay learning rate schedule with warmup."""
        def lr_lambda(current_step):
            if current_step < self.warmup_steps:
                return float(current_step) / float(max(1, self.warmup_steps))
            
            if self.total_steps is None:
                return 1.0
            
            lr_range = 1.0 - 0.1  # Decay to 10% of original LR
            decay_steps = self.total_steps - self.warmup_steps
            pct_remaining = 1 - (current_step - self.warmup_steps) / decay_steps
            decay = lr_range * pct_remaining ** power + 0.1
            return decay
        
        return LambdaLR(self.optimizer, lr_lambda)
    
    def step(self, loss: Optional[torch.Tensor] = None):
        """Optimizer step with dynamic loss scaling."""
        # Check for gradient overflow
        if loss is not None:
            if torch.isnan(loss) or torch.isinf(loss):
                self._handle_gradient_overflow()
                return False
        
        # Check gradient norms
        total_norm = 0
        for group in self.param_groups:
            for param in group["params"]:
                if param.grad is not None:
                    param_norm = param.grad.data.norm(2)
                    total_norm += param_norm.item() ** 2
        total_norm = total_norm ** (1. / 2)
        
        # Handle gradient overflow
        if total_norm > 1000 or torch.isnan(torch.tensor(total_norm)):
            self._handle_gradient_overflow()
            return False
        
        # Normal step
        self.optimizer.step()
        if self.scheduler:
            self.scheduler.step()
        
        # Update loss scaling
        self._update_loss_scale(overflow=False)
        
        return True
    
    def zero_grad(self):
        """Zero gradients."""
        self.optimizer.zero_grad()
    
    def state_dict(self):
        """Get optimizer state dict."""
        return {
            "optimizer": self.optimizer.state_dict(),
            "scheduler": self.scheduler.state_dict() if self.scheduler else None,
            "loss_scale": self.loss_scale,
            "scale_up_counter": self.scale_up_counter,
            "scale_down_counter": self.scale_down_counter,
        }
    
    def load_state_dict(self, state_dict):
        """Load optimizer state dict."""
        self.optimizer.load_state_dict(state_dict["optimizer"])
        if self.scheduler and state_dict["scheduler"]:
            self.scheduler.load_state_dict(state_dict["scheduler"])
        self.loss_scale = state_dict.get("loss_scale", 1.0)
        self.scale_up_counter = state_dict.get("scale_up_counter", 0)
        self.scale_down_counter = state_dict.get("scale_down_counter", 0)
    
    def get_lr(self) -> float:
        """Get current learning rate."""
        return self.optimizer.param_groups[0]["lr"]
    
    def _handle_gradient_overflow(self):
        """Handle gradient overflow by scaling down loss scale."""
        self.zero_grad()
        self._update_loss_scale(overflow=True)
    
    def _update_loss_scale(self, overflow: bool):
        """Update dynamic loss scaling."""
        if overflow:
            self.loss_scale = max(self.loss_scale / self.loss_scale_factor, self.min_loss_scale)
            self.scale_down_counter += 1
            self.scale_up_counter = 0
        else:
            self.scale_up_counter += 1
            if self.scale_up_counter >= self.loss_scale_window:
                self.loss_scale *= self.loss_scale_factor
                self.scale_up_counter = 0
    
    def scale_loss(self, loss: torch.Tensor) -> torch.Tensor:
        """Scale loss for mixed precision training."""
        return loss * self.loss_scale
    
    def unscale_gradients(self):
        """Unscale gradients after backward pass."""
        for group in self.param_groups:
            for param in group["params"]:
                if param.grad is not None:
                    param.grad.data.div_(self.loss_scale)


class AdaptiveLRScheduler:
    """Adaptive learning rate scheduler based on training metrics."""
    
    def __init__(
        self,
        optimizer: torch.optim.Optimizer,
        patience: int = 5,
        factor: float = 0.5,
        min_lr: float = 1e-7,
        threshold: float = 1e-4,
    ):
        self.optimizer = optimizer
        self.patience = patience
        self.factor = factor
        self.min_lr = min_lr
        self.threshold = threshold
        
        self.best_loss = float('inf')
        self.wait_count = 0
        self.step_count = 0
    
    def step(self, loss: float):
        """Step the scheduler based on validation loss."""
        self.step_count += 1
        
        if loss < self.best_loss - self.threshold:
            self.best_loss = loss
            self.wait_count = 0
        else:
            self.wait_count += 1
            
            if self.wait_count >= self.patience:
                self._reduce_lr()
                self.wait_count = 0
    
    def _reduce_lr(self):
        """Reduce learning rate for all parameter groups."""
        for group in self.optimizer.param_groups:
            old_lr = group['lr']
            new_lr = max(old_lr * self.factor, self.min_lr)
            group['lr'] = new_lr
            print(f"Reducing learning rate from {old_lr:.2e} to {new_lr:.2e}")
    
    def state_dict(self):
        """Get scheduler state."""
        return {
            'best_loss': self.best_loss,
            'wait_count': self.wait_count,
            'step_count': self.step_count,
        }
    
    def load_state_dict(self, state_dict):
        """Load scheduler state."""
        self.best_loss = state_dict['best_loss']
        self.wait_count = state_dict['wait_count']
        self.step_count = state_dict['step_count']
