"""
FastLLaMA Trainer with memory optimizations and multi-phase training.

Implements the training strategy from the system design with:
- Phase-based training (Foundation -> Long Context -> Efficiency Fine-tuning)
- Memory optimizations (gradient checkpointing, mixed precision)
- Dynamic batching and sequence length scaling
"""

import os
import time
import torch
import torch.nn as nn
import torch.distributed as dist
from torch.utils.data import DataLoader
from torch.cuda.amp import GradScaler, autocast
from typing import Dict, List, Optional, Union, Any
import logging
from dataclasses import dataclass

from ..config import FastLLaMAConfig
from ..models import FastLLaMAModel
from .optimizers import FastLLaMAOptimizer
from .strategies import PhaseBasedStrategy


@dataclass
class TrainingArguments:
    """Training arguments for FastLLaMA."""

    # Basic training settings
    output_dir: str = "./fastllama_output"
    num_train_epochs: int = 3
    max_steps: int = -1  # If > 0, overrides num_train_epochs
    per_device_train_batch_size: int = 4
    per_device_eval_batch_size: int = 4
    gradient_accumulation_steps: int = 1
    learning_rate: float = 5e-5
    weight_decay: float = 0.01
    warmup_steps: int = 1000

    # Memory optimization
    use_mixed_precision: bool = True
    gradient_checkpointing: bool = True
    max_grad_norm: float = 1.0

    # Sequence length scheduling
    initial_seq_length: int = 2048
    max_seq_length: int = 32768
    seq_length_warmup_steps: int = 5000

    # Evaluation and logging
    eval_steps: int = 500
    save_steps: int = 1000
    logging_steps: int = 100

    # Phase-based training
    foundation_phase_ratio: float = 0.7
    long_context_phase_ratio: float = 0.2
    efficiency_phase_ratio: float = 0.1

    # Hardware settings
    dataloader_num_workers: int = 4
    local_rank: int = -1

    # Early exit training
    early_exit_loss_weight: float = 0.1
    confidence_loss_weight: float = 0.05


class FastLLaMATrainer:
    """
    FastLLaMA trainer with optimized training strategies.
    """

    def __init__(
        self,
        model: FastLLaMAModel,
        config: FastLLaMAConfig,
        args: TrainingArguments,
        train_dataset=None,
        eval_dataset=None,
        tokenizer=None,
    ):
        self.model = model
        self.config = config
        self.args = args
        self.train_dataset = train_dataset
        self.eval_dataset = eval_dataset
        self.tokenizer = tokenizer

        # Setup logging
        self.logger = logging.getLogger(__name__)

        # Setup device
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model.to(self.device)

        # Setup distributed training
        self.is_distributed = args.local_rank != -1
        if self.is_distributed:
            self.model = nn.parallel.DistributedDataParallel(
                self.model, device_ids=[args.local_rank], output_device=args.local_rank
            )

        # Setup optimizer
        self.optimizer = FastLLaMAOptimizer(
            model=self.model,
            learning_rate=args.learning_rate,
            weight_decay=args.weight_decay,
            warmup_steps=args.warmup_steps,
        )

        # Setup mixed precision
        self.scaler = GradScaler() if args.use_mixed_precision else None

        # Setup training strategy
        self.training_strategy = PhaseBasedStrategy(config, args)

        # Training state
        self.global_step = 0
        self.epoch = 0
        self.best_eval_loss = float('inf')

        # Metrics tracking
        self.training_metrics = {
            "loss": [],
            "learning_rate": [],
            "sequence_length": [],
            "memory_usage": [],
            "throughput": [],
        }

    def train(self):
        """Main training loop with phase-based strategy."""
        self.logger.info("Starting FastLLaMA training...")

        # Calculate total steps
        if self.args.max_steps > 0:
            # Use max_steps if specified
            total_steps = self.args.max_steps
        else:
            try:
                # For non-streaming datasets
                total_steps = len(self.train_dataset) // (
                    self.args.per_device_train_batch_size * self.args.gradient_accumulation_steps
                ) * self.args.num_train_epochs
            except TypeError:
                # For streaming datasets, estimate based on epochs
                total_steps = 10000 * self.args.num_train_epochs  # Default for streaming datasets

        # Phase-based training
        phases = self.training_strategy.get_training_phases(total_steps)

        for phase_name, phase_config in phases.items():
            self.logger.info(f"Starting {phase_name} phase...")
            self._train_phase(phase_config)

        self.logger.info("Training completed!")
        return self.training_metrics

    def _train_phase(self, phase_config: Dict[str, Any]):
        """Train a specific phase."""
        # Update model configuration for this phase
        self._update_model_for_phase(phase_config)

        # Create data loader for this phase
        train_dataloader = self._create_dataloader(
            self.train_dataset,
            batch_size=getattr(phase_config, "batch_size", self.args.per_device_train_batch_size),
            seq_length=getattr(phase_config, "seq_length", self.args.initial_seq_length),
        )

        phase_steps = phase_config.steps
        start_step = self.global_step

        self.model.train()

        for step, batch in enumerate(train_dataloader):
            if step >= phase_steps:
                break

            # Move batch to device
            batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}

            # Forward pass
            loss = self._training_step(batch, phase_config)

            # Backward pass
            if self.scaler is not None:
                self.scaler.scale(loss).backward()

                if (step + 1) % self.args.gradient_accumulation_steps == 0:
                    self.scaler.unscale_(self.optimizer.optimizer)
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.args.max_grad_norm)
                    self.scaler.step(self.optimizer.optimizer)
                    self.scaler.update()
                    self.optimizer.zero_grad()
            else:
                loss.backward()

                if (step + 1) % self.args.gradient_accumulation_steps == 0:
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.args.max_grad_norm)
                    self.optimizer.step()
                    self.optimizer.zero_grad()

            self.global_step += 1

            # Logging
            if self.global_step % self.args.logging_steps == 0:
                self._log_metrics(loss, phase_config)

            # Evaluation
            if self.global_step % self.args.eval_steps == 0:
                self._evaluate()

            # Save checkpoint
            if self.global_step % self.args.save_steps == 0:
                self._save_checkpoint()

    def _training_step(self, batch: Dict[str, torch.Tensor], phase_config) -> torch.Tensor:
        """Single training step with mixed precision."""
        if self.scaler is not None:
            with autocast():
                outputs = self.model(**batch)
                loss = self._compute_loss(outputs, batch, phase_config)
        else:
            outputs = self.model(**batch)
            loss = self._compute_loss(outputs, batch, phase_config)

        return loss / self.args.gradient_accumulation_steps

    def _compute_loss(self, outputs: Dict[str, Any], batch: Dict[str, torch.Tensor], phase_config) -> torch.Tensor:
        """Compute loss with early exit and compression losses."""
        # Main language modeling loss
        main_loss = outputs["loss"]
        total_loss = main_loss

        # Early exit losses (if enabled)
        if outputs.get("early_exit_outputs") and getattr(phase_config, "train_early_exit", False):
            early_exit_loss = 0
            confidence_loss = 0

            for early_output in outputs["early_exit_outputs"]:
                # Early exit prediction loss
                early_logits = early_output["logits"]
                shift_logits = early_logits[..., :-1, :].contiguous()
                shift_labels = batch["labels"][..., 1:].contiguous()

                loss_fct = nn.CrossEntropyLoss()
                early_loss = loss_fct(shift_logits.view(-1, self.config.vocab_size), shift_labels.view(-1))
                early_exit_loss += early_loss

                # Confidence calibration loss
                confidence = early_output["confidence"]
                # Encourage high confidence for correct predictions
                target_confidence = (early_loss < main_loss).float()
                conf_loss = nn.MSELoss()(confidence.squeeze(), target_confidence)
                confidence_loss += conf_loss

            total_loss += self.args.early_exit_loss_weight * early_exit_loss
            total_loss += self.args.confidence_loss_weight * confidence_loss

        return total_loss

    def _evaluate(self):
        """Evaluation loop."""
        if self.eval_dataset is None:
            return

        self.model.eval()
        eval_dataloader = self._create_dataloader(self.eval_dataset, is_eval=True)

        total_loss = 0
        num_batches = 0

        with torch.no_grad():
            for batch in eval_dataloader:
                batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}

                if self.scaler is not None:
                    with autocast():
                        outputs = self.model(**batch)
                        loss = outputs["loss"]
                else:
                    outputs = self.model(**batch)
                    loss = outputs["loss"]

                total_loss += loss.item()
                num_batches += 1

        avg_eval_loss = total_loss / num_batches
        self.logger.info(f"Eval loss: {avg_eval_loss:.4f}")

        # Save best model
        if avg_eval_loss < self.best_eval_loss:
            self.best_eval_loss = avg_eval_loss
            self._save_checkpoint(is_best=True)

        self.model.train()

    def _create_dataloader(self, dataset, batch_size=None, seq_length=None, is_eval=False):
        """Create data loader with dynamic batching."""
        batch_size = batch_size or (self.args.per_device_eval_batch_size if is_eval else self.args.per_device_train_batch_size)

        # Dynamic batching based on sequence length and memory
        if self.config.memory_aware_batching and not is_eval:
            # Adjust batch size based on sequence length
            if seq_length and seq_length > self.args.initial_seq_length:
                scale_factor = self.args.initial_seq_length / seq_length
                batch_size = max(1, int(batch_size * scale_factor))

        # Check if dataset is iterable (streaming)
        from torch.utils.data import IterableDataset

        if isinstance(dataset, IterableDataset):
            # For streaming datasets, don't use shuffle (it's handled in the dataset)
            return DataLoader(
                dataset,
                batch_size=batch_size,
                num_workers=self.args.dataloader_num_workers,
                pin_memory=True,
            )
        else:
            # For regular datasets, use shuffle
            return DataLoader(
                dataset,
                batch_size=batch_size,
                shuffle=not is_eval,
                num_workers=self.args.dataloader_num_workers,
                pin_memory=True,
            )

    def _update_model_for_phase(self, phase_config):
        """Update model configuration for training phase."""
        # Enable/disable features based on phase
        if hasattr(self.model, 'module'):
            model = self.model.module
        else:
            model = self.model

        # Update context compression settings
        if getattr(phase_config, "enable_compression", True) and model.context_compressor:
            model.context_compressor.training = True

        # Update early exit settings
        model.config.enable_early_exit = getattr(phase_config, "train_early_exit", False)

    def _log_metrics(self, loss: torch.Tensor, phase_config):
        """Log training metrics."""
        # Memory usage
        if torch.cuda.is_available():
            memory_used = torch.cuda.max_memory_allocated() / 1024**3  # GB
            torch.cuda.reset_peak_memory_stats()
        else:
            memory_used = 0

        # Learning rate
        lr = self.optimizer.get_lr()

        # Sequence length
        seq_length = getattr(phase_config, "seq_length", self.args.initial_seq_length)

        self.logger.info(
            f"Step {self.global_step}: loss={loss.item():.4f}, lr={lr:.2e}, "
            f"seq_len={seq_length}, memory={memory_used:.2f}GB"
        )

        # Store metrics
        self.training_metrics["loss"].append(loss.item())
        self.training_metrics["learning_rate"].append(lr)
        self.training_metrics["sequence_length"].append(seq_length)
        self.training_metrics["memory_usage"].append(memory_used)

    def _save_checkpoint(self, is_best=False):
        """Save model checkpoint."""
        checkpoint_dir = os.path.join(self.args.output_dir, f"checkpoint-{self.global_step}")
        os.makedirs(checkpoint_dir, exist_ok=True)

        # Save model
        if hasattr(self.model, 'module'):
            model_to_save = self.model.module
        else:
            model_to_save = self.model

        torch.save(model_to_save.state_dict(), os.path.join(checkpoint_dir, "pytorch_model.bin"))

        # Save config
        self.config.save_pretrained(checkpoint_dir)

        # Save training state
        torch.save({
            "global_step": self.global_step,
            "epoch": self.epoch,
            "optimizer_state_dict": self.optimizer.state_dict(),
            "scaler_state_dict": self.scaler.state_dict() if self.scaler else None,
            "best_eval_loss": self.best_eval_loss,
        }, os.path.join(checkpoint_dir, "training_state.bin"))

        if is_best:
            best_dir = os.path.join(self.args.output_dir, "best_model")
            os.makedirs(best_dir, exist_ok=True)
            torch.save(model_to_save.state_dict(), os.path.join(best_dir, "pytorch_model.bin"))
            self.config.save_pretrained(best_dir)

        self.logger.info(f"Checkpoint saved to {checkpoint_dir}")

    def load_checkpoint(self, checkpoint_path: str):
        """Load model checkpoint."""
        # Load model state
        model_path = os.path.join(checkpoint_path, "pytorch_model.bin")
        if hasattr(self.model, 'module'):
            self.model.module.load_state_dict(torch.load(model_path, map_location=self.device))
        else:
            self.model.load_state_dict(torch.load(model_path, map_location=self.device))

        # Load training state
        training_state_path = os.path.join(checkpoint_path, "training_state.bin")
        if os.path.exists(training_state_path):
            state = torch.load(training_state_path, map_location=self.device)
            self.global_step = state["global_step"]
            self.epoch = state["epoch"]
            self.optimizer.load_state_dict(state["optimizer_state_dict"])
            if self.scaler and state["scaler_state_dict"]:
                self.scaler.load_state_dict(state["scaler_state_dict"])
            self.best_eval_loss = state["best_eval_loss"]

        self.logger.info(f"Checkpoint loaded from {checkpoint_path}")
