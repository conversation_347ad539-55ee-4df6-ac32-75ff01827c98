{"device": "cuda", "hidden_size": 512, "intermediate_size": 1024, "num_attention_heads": 8, "num_key_value_heads": 2, "num_hidden_layers": 6, "vocab_size": 1000, "max_position_embeddings": 2048, "rope_theta": 10000.0, "rope_scaling": {"type": "linear", "factor": 1.0}, "hidden_act": "silu", "rms_norm_eps": 1e-06, "local_attention_window": 512, "sparse_attention_stride": 8, "compression_ratio": 20, "local_layers": [1, 2, 3, 4, 5, 6, 7, 8], "sparse_layers": [9, 10, 11, 12, 13, 14, 15, 16], "hierarchical_layers": [17, 18, 19, 20, 21, 22, 23, 24], "full_attention_layers": [25, 26, 27, 28, 29, 30, 31, 32], "early_exit_layers": [3, 5], "confidence_threshold": 0.8, "enable_early_exit": true, "compression_encoder_layers": 4, "enable_context_compression": true, "progressive_compression": true, "use_gradient_checkpointing": true, "gradient_checkpointing_ratio": 0.5, "use_mixed_precision": true, "kv_cache_quantization": true, "parameter_sharing": true, "initializer_range": 0.02, "use_cache": true, "pad_token_id": null, "bos_token_id": 1, "eos_token_id": 2, "use_flash_attention": true, "use_kernel_fusion": true, "enable_speculative_decoding": true, "max_batch_size": 32, "dynamic_batching": true, "memory_aware_batching": true, "output_attentions": false, "output_hidden_states": false, "use_return_dict": true}